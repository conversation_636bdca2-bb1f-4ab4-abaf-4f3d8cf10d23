import 'package:account_management/account_management.dart';
import 'package:account_management/application/medication_form_bloc/medication_form_bloc.dart';
import 'package:account_management/domain/model/medication_model.dart';
import 'package:account_management/domain/value_objects/dosage_unit.dart';
import 'package:authentication/widgets/text_form_feild.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fpdart/fpdart.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/medications/medication.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:notifications/application/manage_scheduled_notifications_bloc/manage_scheduled_notifications_bloc.dart';
import '../../helpers.dart';

class MedicationFormPage extends StatelessWidget {
  final MedicationModel? medication;
  const MedicationFormPage({Key? key, this.medication}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MedicationFormBloc>(
          create: (context) => getIt<MedicationFormBloc>()
            ..add(MedicationFormEvent.initialized(
                Option.fromNullable(medication))),
        ),
        BlocProvider<ManageScheduledNotificationsBloc>(
          create: (context) => getIt<ManageScheduledNotificationsBloc>(),
        ),
      ],
      child: BlocConsumer<MedicationFormBloc, MedicationFormState>(
        listener: (context, state) {
          state.saveFailureOrSuccessOption.getOrNull()?.mapBoth(
            onLeft: (failure) {
              Fluttertoast.showToast(
                msg: 'Failed to save medication',
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: Colors.red,
                textColor: Colors.white,
              );
            },
            onRight: (_) {
              Navigator.of(context).pop();
            },
          );
        },
        buildWhen: (previous, current) =>
            previous.isEditing != current.isEditing,
        builder: (context, state) {
          return MedicationFormPageScaffold(medication: medication);
        },
      ),
    );
  }
}

class MedicationFormPageScaffold extends StatelessWidget {
  final MedicationModel? medication;
  MedicationFormPageScaffold({Key? key, this.medication}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MedicationFormBloc, MedicationFormState>(
      buildWhen: (previous, current) =>
          previous.medication != current.medication,
      builder: (context, state) {
        final formBloc = context.read<MedicationFormBloc>();
        return Scaffold(
          bottomNavigationBar: Container(
            padding: const EdgeInsets.fromLTRB(20, 10, 20, 25),
            color: Color.fromARGB(255, 247, 224, 255),
            child: ElevatedButton(
              key: const Key('submit_medication_button'),
              onPressed: () {
                formBloc.add(MedicationFormEvent.save());
                if (state.medication.isNotificationEnabled == true &&
                    state.medication.frequencyUnit == 'weekly') {
                  context.read<ManageScheduledNotificationsBloc>().add(
                        ManageScheduledNotificationsEvent
                            .scheduleNotificationsWeekly(
                          daystoBeNotified: state.medication.daystoBeNotified!,
                          timeofDay: state.medication.timeofDay!,
                          body: 'Time to take your medication',
                          notificationGroupId: state.medication.id!,
                        ),
                      );
                } else if (state.medication.isNotificationEnabled == true &&
                    state.medication.frequencyUnit != 'monthly') {
                  context.read<ManageScheduledNotificationsBloc>().add(
                        ManageScheduledNotificationsEvent
                            .scheduleNotificationsDaily(
                          timeofDay: state.medication.timeofDay!,
                          body: 'Time to take your medication',
                          notificationGroupId: state.medication.id!,
                        ),
                      );
                } else if (state.medication.isNotificationEnabled == true &&
                    state.medication.frequencyUnit == 'monthly') {
                  context.read<ManageScheduledNotificationsBloc>().add(
                        ManageScheduledNotificationsEvent
                            .scheduleNotificationsMonthly(
                          dateTime: tz.TZDateTime.from(
                              state.medication.monthlyDateToBeNotified!,
                              tz.local),
                          body: 'Time to take your medication',
                          notificationGroupId: state.medication.id!,
                        ),
                      );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                minimumSize: const Size(200, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              child: Text(
                medication == null ? 'Add New Medication' : 'Update Medication',
                style: const TextStyle(
                    fontSize: 20.0,
                    fontWeight: FontWeight.w400,
                    color: Colors.white),
              ),
            ),
          ),
          appBar: AppBar(
            backgroundColor: Color(0xffFAF2DF),
            automaticallyImplyLeading: false,
            centerTitle: true,
            actions: [
              IconButton(
                padding: EdgeInsets.only(right: 10, top: 10),
                icon: Icon(
                  Icons.close_rounded,
                  size: 30,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
          body: Stack(
            children: [
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xffFAF2DF),
                        Color.fromARGB(255, 247, 224, 255),
                      ],
                    ),
                  ),
                ),
              ),
              SingleChildScrollView(
                child: Form(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        SizedBox(height: 10),
                        Stack(
                          children: [
                            Positioned(
                              child: Text(
                                "Name",
                                style:
                                    TextStyle(color: Colors.black, fontSize: 20),
                              ),
                            ),
                            Positioned(
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 25,
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(top: 10),
                                    child: MedicationAutocompleteField(
                                      initialValue: medication?.name,
                                      onChanged: (value) {
                                        formBloc.add(
                                            MedicationFormEvent.nameChanged(
                                                value));
                                      },
                                      validator: (value) =>
                                          value == null || value.isEmpty
                                              ? 'Missing Information'
                                              : null,
                                    ),

                                    //MyTextFormField(
                                    //   refkey: Key('medication_name_field'),
                                    //   initialvalue: medication?.name,
                                    //   onchanged: (value) =>
                                    //       formBloc.add(MedicationFormEvent.nameChanged(value!)),
                                    //   validator: (value) =>
                                    //   value == null || value.isEmpty ? 'Missing Information' : null,
                                    // ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        Column(
                          children: [
                            Row(
                              children: [
                                Text(
                                  "Schedule & Dosage",
                                  style: TextStyle(
                                      color: Colors.black, fontSize: 20),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Divider(
                                    thickness: 1,
                                    color: Colors.grey[400],
                                  ),
                                ),
                                // Frequency Container
                              ],
                            ),
                            SizedBox(height: 20),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 15, vertical: 10),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0x40000000),
                                    blurRadius: 4.0,
                                    offset: Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text('Dosage',
                                          style: TextStyle(
                                              color: Color(0xFF30285D),
                                              fontSize: 18)),
                                      Container(
                                        padding: EdgeInsets.all(4),
                                        child: Row(
                                          children: [
                                            // Dosage TextField
                                            Container(
                                              height: 40,
                                              width: 70,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  border: Border.all(
                                                      color: Colors.black26)),
                                              alignment: Alignment.center,
                                              child: TextFormField(
                                                initialValue: medication?.dosage,
                                                key: const Key('dosage_field'),
                                                textAlign: TextAlign.center,
                                                decoration: const InputDecoration(
                                                  border: InputBorder.none,
                                                  counterText: '',
                                                  hintText: 'e.g. 2',
                                                  hintStyle: TextStyle(
                                                      fontSize: 18.0,
                                                      color: Colors.grey),
                                                  isCollapsed:
                                                      true, // helps with vertical alignment
                                                  isDense: true,
                                                ),
                                                style: TextStyle(fontSize: 20),
                                                textAlignVertical:
                                                    TextAlignVertical
                                                        .center, // vertical center
                                                maxLength: 5,
                                                keyboardType:
                                                    TextInputType.number,
                                                onChanged: (value) =>
                                                    formBloc.add(
                                                  MedicationFormEvent
                                                      .dosageChanged(value),
                                                ),
                                              ),
                                            ),

                                            // Dosage Unit Dropdown
                                            Container(
                                              width: 90,
                                              height: 40,
                                              child: FormField<DosageUnit>(
                                                validator: (value) =>
                                                    value == null
                                                        ? 'Please select a unit'
                                                        : null,
                                                builder:
                                                    (FormFieldState<DosageUnit>
                                                        field) {
                                                  return InputDecorator(
                                                    decoration:
                                                        const InputDecoration(
                                                      border: InputBorder.none,
                                                      contentPadding:
                                                          EdgeInsets.only(
                                                              bottom: 10.0,
                                                              left: 10),
                                                    ),
                                                    child:
                                                        DropdownButtonHideUnderline(
                                                      child: DropdownButton<
                                                          DosageUnit>(
                                                        alignment:
                                                            AlignmentDirectional
                                                                .centerEnd,
                                                        value: DosageUnit(state
                                                            .medication
                                                            .dosageUnit!),
                                                        dropdownColor:
                                                            AppTheme.primaryColor,
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                                10),
                                                        elevation: 8,
                                                        menuWidth: 90,
                                                        style: const TextStyle(
                                                          fontSize: 20,
                                                          color: Colors.white,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                        ),
                                                        icon: Icon(
                                                            Icons
                                                                .keyboard_arrow_down_rounded,
                                                            size: 30,
                                                            color: AppTheme
                                                                .primaryColor), // selected item style
                                                        selectedItemBuilder:
                                                            (BuildContext
                                                                context) {
                                                          return DosageUnit
                                                              .availableWeightUnits
                                                              .reversed
                                                              .map((DosageUnit
                                                                  unit) {
                                                            return Align(
                                                              child: Text(
                                                                unit.value,
                                                                style: const TextStyle(
                                                                    color: AppTheme
                                                                        .primaryColor),
                                                              ),
                                                            );
                                                          }).toList();
                                                        },
                                                        items: DosageUnit
                                                            .availableWeightUnits
                                                            .reversed
                                                            .map((DosageUnit
                                                                unit) {
                                                          return DropdownMenuItem<
                                                              DosageUnit>(
                                                            value: unit,
                                                            child: Text(
                                                              unit.value,
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .white), // menu items
                                                            ),
                                                          );
                                                        }).toList(),
                                                        onChanged: (newValue) {
                                                          formBloc.add(
                                                            MedicationFormEvent
                                                                .dosageUnitChanged(
                                                                    newValue!
                                                                        .value),
                                                          );
                                                          field.didChange(
                                                              newValue); // updates validation state
                                                        },
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text('Start Date',
                                          style: TextStyle(
                                              color: Color(0xFF30285D),
                                              fontSize: 18)),
                                      DatePickerButton(
                                        initialDate: state.medication.startDate,
                                        onDateSelected: (selectedDate) {
                                          formBloc.add(MedicationFormEvent
                                              .startDateChanged(selectedDate));
                                        },
                                      )
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text('Frequency',
                                          style: TextStyle(
                                              color: Color(0xFF30285D),
                                              fontSize: 18)),
                                      // Frequency Container
                                      Row(children: [
                                        Container(
                                          child: Container(
                                            width: 118,
                                            child: FormField<FrequencyUnit>(
                                              validator: (value) => value == null
                                                  ? 'Please select a unit'
                                                  : null,
                                              builder:
                                                  (FormFieldState<FrequencyUnit>
                                                      field) {
                                                return InputDecorator(
                                                  decoration:
                                                      const InputDecoration(
                                                    border: InputBorder.none,
                                                    contentPadding:
                                                        EdgeInsets.only(
                                                            left: 12, right: 3),
                                                  ),
                                                  child:
                                                      DropdownButtonHideUnderline(
                                                    child: DropdownButton<
                                                        FrequencyUnit>(
                                                      key: Key(
                                                          'frequency_dropdown'),
                                                      value: FrequencyUnit(state
                                                          .medication
                                                          .frequencyUnit!),
                                                      alignment:
                                                          AlignmentDirectional
                                                              .centerEnd,
                                                      dropdownColor:
                                                          AppTheme.primaryColor,
                                                      menuWidth: 100,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      style: const TextStyle(
                                                        fontSize: 18,
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                      ),
                                                      icon: const Icon(
                                                        Icons
                                                            .keyboard_arrow_down_rounded,
                                                        size: 30,
                                                        color:
                                                            AppTheme.primaryColor,
                                                      ),
                                                      selectedItemBuilder:
                                                          (BuildContext context) {
                                                        return FrequencyUnit
                                                            .availableFrequencyUnits
                                                            .map((unit) {
                                                          return Align(
                                                            alignment:
                                                                Alignment.center,
                                                            child: Text(
                                                              unit.value
                                                                      .substring(
                                                                          0, 1)
                                                                      .toUpperCase() +
                                                                  unit.value
                                                                      .substring(
                                                                          1),
                                                              style: const TextStyle(
                                                                  color: AppTheme
                                                                      .primaryColor,
                                                                  fontSize: 20),
                                                            ),
                                                          );
                                                        }).toList();
                                                      },
                                                      items: FrequencyUnit
                                                          .availableFrequencyUnits
                                                          .map((FrequencyUnit
                                                              unit) {
                                                        return DropdownMenuItem<
                                                            FrequencyUnit>(
                                                          value: unit,
                                                          child: Text(
                                                            unit.value
                                                                    .substring(
                                                                        0, 1)
                                                                    .toUpperCase() +
                                                                unit.value
                                                                    .substring(1),
                                                            style: TextStyle(
                                                                color:
                                                                    Colors.white),
                                                          ),
                                                        );
                                                      }).toList(),
                                                      onChanged: (newValue) {
                                                        formBloc.add(
                                                          MedicationFormEvent
                                                              .frequencyUnitChanged(
                                                                  newValue!
                                                                      .value),
                                                        );
                                                        field.didChange(
                                                            newValue); // Sync with validation
                                                      },
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ])
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 10),
                            if (state.medication.frequencyUnit == 'weekly') ...[
                              // Days of the Week
                              Container(
                                height: 110,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(0),
                                  child: Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                          padding: EdgeInsets.only(left: 10),
                                          child: Text(
                                            "Select Days:",
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 18),
                                          )),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceEvenly,
                                        children: [
                                          'Sun',
                                          'Mon',
                                          'Tue',
                                          'Wed',
                                          'Thu',
                                          'Fri',
                                          'Sat'
                                        ]
                                            .map((day) => _buildDayCircle(
                                                day, formBloc, context))
                                            .toList(),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                            if (state.medication.frequencyUnit != 'monthly') ...[
                              // Notification Times
                              Column(
                                children: [
                                  SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        "Timing",
                                        style: TextStyle(
                                            color: Colors.black, fontSize: 20),
                                      ),
                                      const SizedBox(width: 10),
                                      Expanded(
                                        child: Divider(
                                          thickness: 1,
                                          color: Colors.grey[400],
                                        ),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(vertical: 5),
                                    margin: state.medication.timeofDay!.isNotEmpty
                                        ? EdgeInsets.symmetric(vertical: 20)
                                        : EdgeInsets.zero,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(10),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Color(0x40000000),
                                          blurRadius: 4.0,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      children: state.medication.timeofDay!
                                          .asMap()
                                          .entries
                                          .map((entry) =>
                                              _buildNotificationTimeRow(
                                                  context, entry.key, formBloc))
                                          .toList(),
                                    ),
                                  ),
                                ],
                              ),
                              if (state.medication.timeofDay.length < 3 &&
                                  state.medication.frequencyUnit != 'monthly')
                                ElevatedButton(
                                  onPressed: () async {
                                    if (state.medication.daystoBeNotified
                                            .isNotEmpty ||
                                        state.medication.frequencyUnit ==
                                            'daily') {
                                      TimeOfDay? pickedTime =
                                          await showTimePicker(
                                        initialEntryMode:
                                            TimePickerEntryMode.input,
                                        builder: (BuildContext context,
                                            Widget? child) {
                                          return MediaQuery(
                                            data: MediaQuery.of(context).copyWith(
                                                alwaysUse24HourFormat: false),
                                            child: child!,
                                          );
                                        },
                                        context: context,
                                        initialTime: TimeOfDay.now(),
                                      );

                                      if (pickedTime != null) {
                                        final updatedTimes = List<String>.from(
                                            state.medication.timeofDay!);
                                        updatedTimes
                                            .add(pickedTime.format(context));
                                        formBloc.add(
                                            MedicationFormEvent.timeofDayChanged(
                                                updatedTimes));
                                      }
                                    } else {
                                      Fluttertoast.showToast(
                                        msg: "Please select at least one day.",
                                        toastLength: Toast.LENGTH_SHORT,
                                        gravity: ToastGravity.BOTTOM,
                                        backgroundColor: Colors.orange,
                                        textColor: Colors.white,
                                      );
                                    }
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    padding: EdgeInsets.symmetric(
                                        vertical: 14, horizontal: 20),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    elevation: 4,
                                    shadowColor: Color(0x40000000),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.add_rounded,
                                          color: AppTheme.primaryColor, size: 28),
                                      SizedBox(width: 8),
                                      Text(
                                        'Add Medication Time',
                                        style: TextStyle(
                                          color: AppTheme.primaryColor,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],

                            // Notification Date for Monthly date time picker
                            if (state.medication.frequencyUnit == 'monthly') ...[
                              Container(
                                height: 110,
                                margin: EdgeInsets.only(bottom: 15),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(
                                      height: 12,
                                    ),
                                    Container(
                                        padding: EdgeInsets.only(left: 10),
                                        child: Text(
                                          "Select Day of the Month & Time:",
                                          textAlign: TextAlign.start,
                                          style: TextStyle(
                                              color: Colors.black, fontSize: 18),
                                        )),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 20.0, vertical: 6.0),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Color(0xFFD6D5F8),
                                          borderRadius: BorderRadius.circular(30),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Color(0x40000000),
                                              blurRadius: 4.0,
                                              offset: Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 20.0, vertical: 3.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                  "Day: ${DateFormat('dd').format(state.medication.monthlyDateToBeNotified ?? DateTime.now())}, Time: ${DateFormat('HH:mm a').format(state.medication.monthlyDateToBeNotified ?? DateTime.now())}",
                                                  style: GoogleFonts.roboto(
                                                    fontSize: 18,
                                                    color: Colors.black,
                                                    fontWeight: FontWeight.w400,
                                                  )),
                                              IconButton(
                                                icon: Icon(Icons.calendar_today),
                                                onPressed: () async {
                                                  final DateTime? pickedDate =
                                                      await showDatePicker(
                                                    context: context,
                                                    initialDate: state.medication
                                                            .monthlyDateToBeNotified ??
                                                        DateTime.now(),
                                                    firstDate: DateTime.now(),
                                                    lastDate: DateTime.now()
                                                        .add(Duration(days: 60)),
                                                  );
                                                  final TimeOfDay? pickedTime =
                                                      await showTimePicker(
                                                    initialEntryMode:
                                                        TimePickerEntryMode.input,
                                                    builder:
                                                        (BuildContext context,
                                                            Widget? child) {
                                                      return MediaQuery(
                                                        data: MediaQuery.of(
                                                                context)
                                                            .copyWith(
                                                                alwaysUse24HourFormat:
                                                                    false),
                                                        child: child!,
                                                      );
                                                    },
                                                    context: context,
                                                    initialTime: TimeOfDay.now(),
                                                  );

                                                  if (pickedDate != null) {
                                                    final updatedDate = DateTime(
                                                      pickedDate.year,
                                                      pickedDate.month,
                                                      pickedDate.day,
                                                      pickedTime?.hour ??
                                                          DateTime.now().hour,
                                                      pickedTime?.minute ??
                                                          DateTime.now().minute,
                                                    );
                                                    formBloc.add(MedicationFormEvent
                                                        .monthlyDateToBeNotifiedChanged(
                                                            updatedDate));
                                                  }
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                            Stack(
                              children: [
                                Positioned(
                                    child: Text('Notes',
                                        style: TextStyle(
                                            color: Colors.black, fontSize: 20))),
                                Positioned(
                                  child: Container(
                                    margin: EdgeInsets.only(top: 10),
                                    child: MyTextFormField(
                                      style: TextStyle(color: Colors.pink),
                                      refkey: Key('notes_field'),
                                      initialvalue: medication?.notes,
                                      maxLines: 1,
                                      maxText: 45,
                                      hintText: 'e.g. Take after eating',
                                      onchanged: (value) => formBloc.add(
                                        MedicationFormEvent.notesChanged(value!),
                                      ),
                                      validator: (value) =>
                                          value == null || value.isEmpty
                                              ? 'Missing Information'
                                              : null,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            SizedBox(
                              height: 15,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Notifications',
                                    style: TextStyle(
                                        color: Colors.black, fontSize: 20)),
                                Switch(
                                  value: state.medication.isNotificationEnabled ??
                                      false,
                                  key: Key('notification_switch'),
                                  onChanged: (value) {
                                    formBloc.add(MedicationFormEvent
                                        .isNotificationEnabledChanged(value));
                                  },
                                  activeColor: Theme.of(context).primaryColor,
                                ),
                              ],
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDayCircle(
      String day, MedicationFormBloc formBloc, BuildContext context) {
    final bool isSelected =
        formBloc.state.medication.daystoBeNotified!.contains(day);
    final List<String> updatedDays =
        List<String>.from(formBloc.state.medication.daystoBeNotified ?? []);
    return GestureDetector(
      onTap: () {
        if (isSelected) {
          updatedDays.remove(day);
          formBloc.add(MedicationFormEvent.daystoBeNotifiedChanged(
            updatedDays,
          ));
        } else {
          updatedDays.add(day);
          formBloc.add(MedicationFormEvent.daystoBeNotifiedChanged(
            updatedDays,
          ));
        }
      },
      child: Container(
        width: 45,
        height: 45,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color:
              isSelected ? Theme.of(context).primaryColor : Color(0xFFD6D5F8),
        ),
        alignment: Alignment.center,
        child: Text(
          day,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationTimeRow(
      BuildContext context, int index, MedicationFormBloc formBloc) {
    final time = formBloc.state.medication.timeofDay![index];
    final updatedTimes =
        List<String>.from(formBloc.state.medication.timeofDay!);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0),
          child: Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.access_time_sharp,
                        size: 30, color: AppTheme.primaryColor),
                    SizedBox(width: 10),
                    Text(
                      time,
                      style: TextStyle(fontSize: 20, color: Colors.black),
                    ),
                  ],
                ),
                IconButton(
                  icon: Icon(Icons.remove_circle_rounded,
                      size: 30, color: AppTheme.primaryColor),
                  onPressed: () {
                    updatedTimes.removeAt(index);
                    formBloc.add(
                        MedicationFormEvent.timeofDayChanged(updatedTimes));
                  },
                ),
              ],
            ),
          ),
        ),
        if (index != updatedTimes.length - 1)
          Divider(
            thickness: 1,
            color: Colors.grey[300],
            indent: 15,
            endIndent: 15,
          ),
      ],
    );
  }
}

class DatePickerButton extends StatelessWidget {
  final DateTime? initialDate;
  final Function(DateTime)? onDateSelected;

  const DatePickerButton({
    Key? key,
    this.initialDate,
    this.onDateSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: initialDate ?? DateTime.now(),
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
        );
        if (selectedDate != null) {
          onDateSelected?.call(selectedDate);
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        elevation: 0, // Remove if you want a shadow
      ),
      icon: const Icon(
        Icons.calendar_today,
        color: AppTheme.primaryColor,
        size: 20,
      ),
      iconAlignment: IconAlignment.end,
      label: Text(
        _formatDate(),
        style: const TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 20,
            fontWeight: FontWeight.w400),
      ),
    );
  }

  String _formatDate() {
    if (initialDate != null) {
      final isToday = initialDate!.year == DateTime.now().year &&
          initialDate!.month == DateTime.now().month &&
          initialDate!.day == DateTime.now().day;
      return isToday ? "Today" : DateFormat('MMM d, y').format(initialDate!);
    } else {
      return 'Select date';
    }
  }
}

final List<String> medicationSuggestions = [
  'Aspirin',
  'Ibuprofen',
  'Paracetamol',
  'Naproxen',
  'Midol',
  'Mefenamic acid',
  'Tranexamic acid',
  'Birth Control',
  'Iron',
  'Magnesium',
  'Omega-3s',
  'Zinc',
  'Iodine',
  'Progesterone',
  'CBD',
  'Taurine',
  'Ginger',
  'Calcium',
  'Vitamin B',
  'Vitamin D',
  'Vitamin C',
  'Melatonin'
];

class MedicationAutocompleteField extends StatelessWidget {
  final String? initialValue;
  final void Function(String) onChanged;
  final String? Function(String?)? validator;

  const MedicationAutocompleteField({
    super.key,
    required this.initialValue,
    required this.onChanged,
    required this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Autocomplete<String>(
      initialValue: TextEditingValue(text: initialValue ?? ''),
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return const Iterable<String>.empty();
        }
        return medicationSuggestions.where((String option) {
          return option
              .toLowerCase()
              .contains(textEditingValue.text.toLowerCase());
        });
      },
      onSelected: (String selection) {
        onChanged(selection); // Triggers your bloc event
      },
      fieldViewBuilder: (
        BuildContext context,
        TextEditingController textEditingController,
        FocusNode focusNode,
        VoidCallback onFieldSubmitted,
      ) {
        return TextFormField(
          key: const Key('medication_name_field'),
          controller: textEditingController,
          autocorrect: false,
          maxLines: 1,
          decoration: InputDecoration(
            floatingLabelAlignment: FloatingLabelAlignment.start,
            filled: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 0),
            fillColor: Colors.white,
            errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Colors.red, width: 1)),
            disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F), width: 1)),
            enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F), width: 1)),
            border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F), width: 1)),
            focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F), width: 1)),
            errorMaxLines: 1,
            floatingLabelStyle: GoogleFonts.poppins(
              color: Colors.black,
            ),
          ),
          focusNode: focusNode,
          onChanged: onChanged,
          validator: validator,
        );
      },
      optionsViewBuilder: (
        BuildContext context,
        AutocompleteOnSelected<String> onSelected,
        Iterable<String> options,
      ) {
        return Align(
          alignment: Alignment.topLeft,
          child: Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Material(
              elevation: 4,
              color: Colors.white,
              borderRadius:
                  BorderRadius.circular(10), // Dropdown background color
              child: Container(
                width: MediaQuery.of(context).size.width - 32,
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: options.length < 5 ? options.length : 4,
                  shrinkWrap: true,
                  itemBuilder: (BuildContext context, int index) {
                    final option = options.elementAt(index);
                    return InkWell(
                      onTap: () => onSelected(option),
                      child: Container(
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade200),
                          ),
                        ),
                        child: Text(
                          option,
                          style: const TextStyle(
                            color: Colors.black, // Text color
                            fontSize: 16,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// class ReminderPickerWidget extends StatefulWidget {
//   const ReminderPickerWidget({super.key});
//
//   @override
//   State<ReminderPickerWidget> createState() => _ReminderPickerWidgetState();
// }
//
// class _ReminderPickerWidgetState extends State<ReminderPickerWidget> {
//   List<TimeOfDay> reminders = [];
//
//   void _addReminder() {
//     if (reminders.length < 3) {
//       setState(() {
//         reminders.add(TimeOfDay(hour: 9, minute: 0));
//       });
//     } else {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text("You can only add up to 3 reminders")),
//       );
//     }
//   }
//
//   void _updateReminder(int index, TimeOfDay newTime) {
//     setState(() {
//       reminders[index] = newTime;
//     });
//   }
//
//   void _removeReminder(int index) {
//     setState(() {
//       reminders.removeAt(index);
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Container(
//           margin: EdgeInsets.only(top: 5, bottom: 5),
//           padding: EdgeInsets.symmetric(vertical: 8),
//           decoration: BoxDecoration(
//             color: reminders.isEmpty ? Colors.transparent : Colors.white,
//             borderRadius: BorderRadius.circular(10),
//             boxShadow: reminders.isEmpty
//                 ? []
//                 : [
//               BoxShadow(
//                 color: Color(0x40000000),
//                 blurRadius: 4.0,
//                 offset: Offset(0, 1),
//               ),
//             ],
//           ),
//           child: Column(
//             children: List.generate(reminders.length, (index) {
//               final time = reminders[index];
//               return Column(
//                 children: [
//                   Padding(
//                     padding: EdgeInsets.symmetric(horizontal: 15, vertical: 4),
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         GestureDetector(
//                           onTap: () async {
//                             TimeOfDay? picked = await showTimePicker(
//                               context: context,
//                               initialTime: time,
//                             );
//                             if (picked != null) {
//                               _updateReminder(index, picked);
//                             }
//                           },
//                           child: Row(
//                             children: [
//                               Icon(Icons.access_time_sharp, size: 30, color: AppTheme.primaryColor),
//                               SizedBox(width: 10),
//                               Text(
//                                 time.format(context),
//                                 style: TextStyle(fontSize: 20, color: Colors.black),
//                               ),
//                             ],
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () => _removeReminder(index),
//                           child: Icon(Icons.remove_circle_rounded, size: 30, color: AppTheme.primaryColor),
//                         ),
//                       ],
//                     ),
//                   ),
//                   // Show divider unless it's the last item
//                   if (index != reminders.length - 1)
//                     Divider(
//                       thickness: 1,
//                       color: Colors.grey[300],
//                       indent: 15,
//                       endIndent: 15,
//                     ),
//                 ],
//               );
//             }),
//           ),
//         ),
//         ElevatedButton(
//           onPressed: _addReminder,
//           style: ElevatedButton.styleFrom(
//             backgroundColor: Colors.white,
//             padding: EdgeInsets.symmetric(vertical: 14, horizontal: 20),
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(15),
//             ),
//             elevation: 4,
//             shadowColor: Color(0x40000000),
//           ),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Icon(Icons.add_rounded, color: AppTheme.primaryColor, size: 28),
//               SizedBox(width: 8),
//               Text(
//                 'Add Medication Time',
//                 style: TextStyle(
//                   color: AppTheme.primaryColor,
//                   fontSize: 16,
//                   fontWeight: FontWeight.w400,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
// }
