import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/facade/health_data_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';

part 'period_tracking_watcher_event.dart';
part 'period_tracking_watcher_state.dart';
part 'period_tracking_watcher_bloc.freezed.dart';

@injectable
class PeriodTrackingWatcherBloc
    extends Bloc<PeriodTrackingWatcherEvent, PeriodTrackingWatcherState> {
  final PeriodTrackingFacade _periodTrackingFacade;
  final HealthDataFacade _healthDataFacade;
  StreamSubscription<
          Either<PeriodTrackingFailure,
              Map<String, Map<String, PeriodTrackingModel>>>>?
      _periodStreamSubscription;

  PeriodTrackingWatcherBloc(this._periodTrackingFacade, this._healthDataFacade)
      : super(const PeriodTrackingWatcherState.initial()) {
    on<_WatchYearStarted>(_onWatchYearStarted);
    on<_PeriodDataReceived>(_onPeriodDataReceived);
  }

  Future<void> _onWatchYearStarted(
    _WatchYearStarted event,
    Emitter<PeriodTrackingWatcherState> emit,
  ) async {
    emit(const PeriodTrackingWatcherState.loading());
    await _periodStreamSubscription?.cancel();

    _periodStreamSubscription =
        _periodTrackingFacade.watchYearData(event.year).listen(
      (failureOrData) {
        if (isClosed) return;
        add(PeriodTrackingWatcherEvent.periodDataReceived(failureOrData));
      },
    );
  }

  Future<void> _onPeriodDataReceived(
    _PeriodDataReceived event,
    Emitter<PeriodTrackingWatcherState> emit,
  ) async {
    final failure = event.failureOrData.getLeft().getOrNull();
    if (failure != null) {
      emit(PeriodTrackingWatcherState.loadFailure(failure));
    } else {
      final data = event.failureOrData
          .getOrElse((_) => <String, Map<String, PeriodTrackingModel>>{});

      // Calculate future predictions synchronously to avoid async issues
      final futurePredictions = _calculateFuturePredictionsSync(data);

      if (!emit.isDone) {
        emit(PeriodTrackingWatcherState.loadSuccess(data,
            futurePredictions: futurePredictions));
      }
    }
  }

  /// Calculate future period and ovulation predictions until end of year (synchronous)
  Map<String, Set<DateTime>> _calculateFuturePredictionsSync(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    try {
      // Use default values for now - health data will be fetched separately
      int cycleLength = 28; // Default cycle length
      int periodLength = 5; // Default period length

      // Extract existing period dates from year data
      final existingPeriodDates = <DateTime>{};
      try {
        for (final monthEntry in yearData.entries) {
          for (final dayEntry in monthEntry.value.entries) {
            final periodModel = dayEntry.value;
            if (periodModel.isPeriodDate == true && periodModel.date != null) {
              existingPeriodDates.add(periodModel.date!);
            }
          }
        }
      } catch (e) {
        // If there's an error extracting dates, continue with empty set
        print('Error extracting period dates: $e');
      }

      final now = DateTime.now();
      final endOfYear = DateTime(now.year, 12, 31);
      final futurePeriodDates = <DateTime>{};
      final futureOvulationDates = <DateTime>{};

      // Find the most recent period date or use tomorrow if no periods exist
      DateTime? lastPeriodDate;
      if (existingPeriodDates.isNotEmpty) {
        try {
          final sortedDates = existingPeriodDates.toList()..sort();
          // Find the most recent period start (first day of a period cycle)
          lastPeriodDate = _findMostRecentPeriodStart(sortedDates);
        } catch (e) {
          print('Error finding recent period start: $e');
        }
      }

      // If no recent period found or it's more than a month ago, use tomorrow
      if (lastPeriodDate == null ||
          now.difference(lastPeriodDate).inDays > 35) {
        lastPeriodDate = now.add(const Duration(days: 1));
      }

      // Calculate future periods until end of year
      DateTime nextPeriodStart =
          lastPeriodDate.add(Duration(days: cycleLength));

      // Safety counter to prevent infinite loops
      int safetyCounter = 0;
      const maxIterations = 20; // Max ~1.5 years of predictions

      while ((nextPeriodStart.isBefore(endOfYear) ||
              nextPeriodStart.isAtSameMomentAs(endOfYear)) &&
          safetyCounter < maxIterations) {
        safetyCounter++;

        // Only add future dates (after today)
        if (nextPeriodStart.isAfter(now)) {
          // Add period dates
          for (int i = 0; i < periodLength && i < 10; i++) {
            // Max 10 days period
            final periodDate = nextPeriodStart.add(Duration(days: i));
            if (periodDate.isBefore(endOfYear) ||
                periodDate.isAtSameMomentAs(endOfYear)) {
              futurePeriodDates.add(periodDate);
            }
          }

          // Add ovulation window (4 days around 14 days before next cycle)
          // Make sure ovulation day is reasonable (between 7-21 days)
          final ovulationDay = (cycleLength - 14).clamp(7, 21);
          final ovulationStartDate = nextPeriodStart
              .add(Duration(days: ovulationDay - 1)); // Start 1 day earlier

          // Add 4-day ovulation window
          for (int j = 0; j < 4; j++) {
            final ovulationDate = ovulationStartDate.add(Duration(days: j));
            if (ovulationDate.isAfter(now) &&
                (ovulationDate.isBefore(endOfYear) ||
                    ovulationDate.isAtSameMomentAs(endOfYear))) {
              futureOvulationDates.add(ovulationDate);
            }
          }
        }

        nextPeriodStart = nextPeriodStart.add(Duration(days: cycleLength));
      }

      return {
        'periods': futurePeriodDates,
        'ovulations': futureOvulationDates,
      };
    } catch (e) {
      // Return empty predictions if calculation fails
      return {
        'periods': <DateTime>{},
        'ovulations': <DateTime>{},
      };
    }
  }

  /// Find the most recent period start date from existing period dates
  DateTime? _findMostRecentPeriodStart(List<DateTime> sortedPeriodDates) {
    if (sortedPeriodDates.isEmpty) return null;

    final now = DateTime.now();

    // Group consecutive dates into cycles
    final cycles = <List<DateTime>>[];
    List<DateTime> currentCycle = [sortedPeriodDates.first];

    for (int i = 1; i < sortedPeriodDates.length; i++) {
      final currentDate = sortedPeriodDates[i];
      final previousDate = sortedPeriodDates[i - 1];

      // If dates are consecutive (within 2 days), add to current cycle
      if (currentDate.difference(previousDate).inDays <= 2) {
        currentCycle.add(currentDate);
      } else {
        // Start a new cycle
        cycles.add(currentCycle);
        currentCycle = [currentDate];
      }
    }
    cycles.add(currentCycle);

    // Find the most recent cycle that's not too far in the future
    for (final cycle in cycles.reversed) {
      final cycleStart = cycle.first;
      if (cycleStart.isBefore(now) || cycleStart.isAtSameMomentAs(now)) {
        return cycleStart;
      }
    }

    return null;
  }

  @override
  Future<void> close() async {
    await _periodStreamSubscription?.cancel();
    return super.close();
  }
}
